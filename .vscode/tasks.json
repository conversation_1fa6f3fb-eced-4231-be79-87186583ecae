// Available variables which can be used inside of strings.
// ${workspaceRoot}: the root folder of the team
// ${file}: the current opened file
// ${fileBasename}: the current opened file's basename
// ${fileDirname}: the current opened file's dirname
// ${fileExtname}: the current opened file's extension
// ${cwd}: the current working directory of the spawned process
// A task runner that calls a custom npm script that compiles the extension.
{
  "version": "2.0.0",
  "presentation": {
    "echo": false,
    "reveal": "always",
    "focus": false,
    "panel": "dedicated",
    "showReuseMessage": false
  },
  "tasks": [
    {
      "type": "npm",
      "script": "build",
      "group": "build",
      "problemMatcher": ["$ts-checker-webpack", "$ts-checker-eslint-webpack"]
    },
    {
      "type": "npm",
      "script": "lint",
      "group": "build",
      "problemMatcher": ["$eslint-stylish"]
    },
    {
      "type": "npm",
      "script": "watch",
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "isBackground": true,
      "problemMatcher": ["$ts-checker-webpack-watch", "$ts-checker-eslint-webpack-watch"]
    }
  ]
}
