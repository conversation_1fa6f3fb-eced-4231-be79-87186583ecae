.devcontainer/**
.github/**
.vscode/**
.vscode-clean/**
.vscode-test/**
.vscode-test-web/**
docs/**
node_modules/**
out/**
scripts/**
src/**
tests/**
**/*.js.LICENSE.txt
**/*.map
**/*.pdn
.eslintcache
.DS_Store
.browserslistrc
.eslintignore
.fantasticonrc.js
.git-blame-ignore-revs
.gitattributes
.gitignore
.mailmap
.nvmrc
.prettierignore
.prettierrc
.vscode-test.mjs
CODE_OF_CONDUCT.md
CONTRIBUTING.md
eslint.config.mjs
pnpm-lock.yaml
svgo.config.js
tsconfig*.json
tsconfig*.tsbuildinfo
webpack.config*.mjs
