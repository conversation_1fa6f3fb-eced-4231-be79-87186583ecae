{"compilerOptions": {"baseUrl": ".", "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "lib": ["es2022", "esnext.disposable"], "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "outDir": "dist", "resolveJsonModule": true, "rootDir": "src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es2022", "useDefineForClassFields": true, "useUnknownInCatchVariables": false}}